import React, { useState, useRef, useEffect } from 'react';
import { YTHList, YTHLocalization } from 'yth-ui';
import { Button, message, Form, Row, Col, DatePicker, Card, Spin } from 'antd';
import type { FormInstance } from 'antd/es/form';
import type { Moment } from 'moment';
import moment from 'moment';
import * as echarts from 'echarts';
import type {
  ECharts,
  EChartsOption,
  TooltipComponentOption,
  ToolboxComponentOption,
  LineSeriesOption,
} from 'echarts';

import locales from '@/locales';
import statisticsApi from '@/service/statisticsApi';
import {
  InspectionAlarmStatisticsVo,
  InspectionSumVo,
  InspectionTaskEchartsLineDataVo,
  QueryParams,
  TrendDayData,
} from '@/types/statistics';
import { BaseResponse } from '@/types/common';
import style from './statistics.module.less';
import PlanListDialog from './components/planListDialog';

interface PieDataItem {
  name: string;
  value: number;
}
interface FormValues {
  startDate?: Moment;
  endDate?: Moment;
}

/**
 * @description 电子运单统计分析
 */
const ElecWaybillList: React.FC = () => {
  const aa: ReturnType<typeof YTHList.createAction> = YTHList.createAction();
  const [form]: [FormInstance] = Form.useForm();
  const echartsRef: React.MutableRefObject<ECharts | null> = useRef<ECharts>(null);
  const echartsPie1Ref: React.MutableRefObject<ECharts | null> = useRef<ECharts>(null);
  const [searchParams, setSearchParams] = useState<QueryParams>(null);
  const [planDialogVisiable, setPlanDialogVisiable] = useState<boolean>(false);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [sumData, setSumData] = useState<InspectionSumVo>({
    /** 巡检计划总数 */
    totalPlanNum: 0,
    /** 巡检任务总数 */
    totalTaskNum: 0,
    /** 未完成巡检任务总数 */
    notFinishedTaskNum: 0,
    /** 已完成巡检任务总数 */
    finishedTaskNum: 0,
    /** 异常数量 */
    totalExceptionNum: 0,
    /** 未处置异常数量 */
    notHandleExceptionNum: 0,
    /** 报警数量 */
    totalAlarmNum: 0,
    /** 未处置报警数量 */
    notHandleAlarmNum: 0,
  });

  /**
   * 动态构建趋势图series
   */
  const getSeries: (data: TrendDayData) => LineSeriesOption[] = (
    data: TrendDayData,
  ): LineSeriesOption[] => {
    const series: LineSeriesOption[] = [];
    if (data?.lineNames && data.yaxisData) {
      data.lineNames.forEach((item: string) => {
        const serie: LineSeriesOption = {
          type: 'line',
          name: item,
          smooth: false,
          data: data.yaxisData[item] || [],
        };
        series.push(serie);
      });
    }
    return series;
  };

  /**
   * 动态获取趋势图配置
   */
  const getLineOption: (data: TrendDayData) => EChartsOption = (
    data: TrendDayData,
  ): EChartsOption => {
    const option: EChartsOption = {
      tooltip: {
        trigger: 'axis',
      } as TooltipComponentOption,
      title: {
        left: 'center',
      },
      legend: {
        left: 'center',
        top: '5%',
      },
      grid: {
        left: 0,
        right: 0,
        bottom: 0,
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
        magicType: { type: ['line', 'bar'] },
      } as ToolboxComponentOption,
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data?.xaxisData || [],
      },
      yAxis: {
        type: 'value',
        name: '数量',
      },
      series: getSeries(data),
    };
    return option;
  };

  /**
   * 获取饼图图配置
   */
  const getPieOption: (title: string, data: PieDataItem[]) => EChartsOption = (
    title: string,
    data: PieDataItem[],
  ): EChartsOption => {
    const option: EChartsOption = {
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 14,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c}  ({d}%)',
      },
      legend: {
        type: 'scroll',
        bottom: 10,
        left: 'center',
      },
      series: [
        {
          name: title,
          type: 'pie',
          radius: ['30%', '65%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            scale: true,
            scaleSize: 10,
          },
          labelLine: {
            show: false,
          },
          data: data || [],
        },
      ],
    };
    return option;
  };

  // 初始化 Echarts
  const initEcharts: () => void = (): void => {
    echartsRef.current?.setOption(getLineOption(null));
    echartsPie1Ref.current?.setOption(getPieOption('告警来源占比', []));
  };
  const handleFilter: () => QueryParams = (): QueryParams => {
    const filter: QueryParams = {};
    const formValues: FormValues = form.getFieldsValue();
    if (formValues?.startDate) {
      const startDate: string = formValues.startDate.format('YYYY-MM-DD');
      filter.startTime = `${startDate} 00:00:00`;
    }
    if (formValues?.endDate) {
      const endDate: string = formValues.endDate.format('YYYY-MM-DD');
      filter.endTime = `${endDate} 23:59:59`;
    }
    return filter;
  };

  // 查询合计数据
  const querySumData: () => Promise<void> = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const queryParams: QueryParams = handleFilter();
      setSearchParams(queryParams);
      const res: BaseResponse<InspectionSumVo> = await statisticsApi.querySumData(queryParams);
      setIsLoading(false);
      if (res?.code === 200 && res.data) {
        setSumData(res.data);
      } else {
        setSumData({
          totalPlanNum: 0,
          totalTaskNum: 0,
          finishedTaskNum: 0,
          notFinishedTaskNum: 0,
          totalExceptionNum: 0,
          notHandleExceptionNum: 0,
          totalAlarmNum: 0,
          notHandleAlarmNum: 0,
        });
      }
    } catch (err) {
      setSumData({
        totalPlanNum: 0,
        totalTaskNum: 0,
        notFinishedTaskNum: 0,
        totalExceptionNum: 0,
        notHandleExceptionNum: 0,
        totalAlarmNum: 0,
        notHandleAlarmNum: 0,
      });
      // eslint-disable-next-line no-console
      console.error('querySumData', err);
    }
  };
  /**
   * 趋势图数据转换
   */
  const trendDayDataConvert: (data: InspectionTaskEchartsLineDataVo) => TrendDayData = (
    data: InspectionTaskEchartsLineDataVo,
  ): TrendDayData => {
    return {
      xaxisData: data.xaxisData,
      lineNames: data.lineNames,
      yaxisData: {
        计划任务数: data.totalTaskValues,
        已完成任务数: data.finishedTaskValues,
        发现异常任务数: data.exceptionTaskValues,
      },
    };
  };
  // 查询趋势图数据
  const queryChartsData: () => Promise<void> = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const queryParams: QueryParams = handleFilter();
      const res: BaseResponse<InspectionTaskEchartsLineDataVo> =
        await statisticsApi.trendDayTask(queryParams);
      setIsLoading(false);
      if (res?.code === 200) {
        const trendData: TrendDayData = trendDayDataConvert(res.data);
        echartsRef.current?.setOption(getLineOption(trendData));
      } else {
        echartsRef.current?.clear();
      }
    } catch (err) {
      echartsRef.current?.clear();
      // eslint-disable-next-line no-console
      console.error('queryChartsData', err);
    }
  };
  // 统计告警来源占比
  const countAlarmSource: () => Promise<void> = async (): Promise<void> => {
    if (echartsPie1Ref.current) {
      echartsPie1Ref.current.clear();
    }
    try {
      setIsLoading(true);
      const queryParams: QueryParams = handleFilter();
      const res: BaseResponse<InspectionAlarmStatisticsVo[]> =
        await statisticsApi.getAlarmSourcePie(queryParams);
      setIsLoading(false);
      if (res && res.code && res.code === 200) {
        const alarmSourcePieData: PieDataItem[] = [];
        for (let i: number = 0; i < (res.data as object[]).length; i += 1) {
          alarmSourcePieData.push({
            name: res.data[i].alarmSource,
            value: res.data[i].totalNum,
          });
        }
        if (echartsPie1Ref.current) {
          echartsPie1Ref.current.setOption(getPieOption('告警来源占比', alarmSourcePieData));
        }
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('getAlarmSourcePie', err);
    }
  };
  // 搜索
  const submitForm: () => Promise<void> = async (): Promise<void> => {
    // 表单校验
    const formData: FormValues = form.getFieldsValue();
    if (!formData.startDate || !formData.endDate) {
      message.error('统计时间不能为空!');
      return;
    }
    // 查询合计数
    await querySumData();
    // 查询表格明细
    aa.reload({});
    // 查询趋势图数据
    await queryChartsData();
    // 统计告警来源占比
    await countAlarmSource();
  };
  // 重置搜索条件
  const formReset: () => void = (): void => {
    form.setFieldsValue({
      startDate: moment().subtract(7, 'days'),
      endDate: moment(),
    });
    submitForm();
  };
  useEffect(() => {
    form.setFieldsValue({
      startDate: moment().subtract(7, 'days'),
      endDate: moment(),
    });

    const development: HTMLElement = document.getElementById('trend-chart') as HTMLElement;
    echartsRef.current = echarts.init(development);

    const pieDom1: HTMLElement = document.getElementById('pie-chart-1') as HTMLElement;
    echartsPie1Ref.current = echarts.init(pieDom1);

    window.addEventListener('resize', () => {
      echartsRef.current.resize();
      echartsPie1Ref.current.resize();
    });
    // 初始Echarts
    initEcharts();
    // 查询数据
    submitForm();
  }, []);

  return (
    <div className={style['car-access-record-page']}>
      <Spin spinning={isLoading}>
        <div className={style['car-access-record-search-box']}>
          <Form form={form} name="carAccessRecordSearchForm">
            <Row gutter={16} style={{ height: 50 }} align="middle">
              <Col span={16} style={{ display: 'flex' }}>
                <Form.Item name="startDate" label="统计时间">
                  <DatePicker
                    style={{ width: '100%', marginRight: '5px' }}
                    format="YYYY-MM-DD"
                    picker="date"
                  />
                </Form.Item>
                <Form.Item name="endDate" label={<span style={{ marginLeft: 8 }}> 至</span>}>
                  <DatePicker style={{ width: '100%' }} picker="date" format="YYYY-MM-DD" />
                </Form.Item>
              </Col>

              <Col span={8} style={{ textAlign: 'end' }}>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                  <Button
                    style={{ margin: '0 8px', width: 96 }}
                    type="primary"
                    onClick={submitForm}
                  >
                    查询
                  </Button>
                  <Button style={{ margin: '0 8px', width: 96 }} onClick={formReset}>
                    重置
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </div>
        <div className={style['sum-card-box']}>
          <Row align="middle">
            <Col span={3}>
              <Row
                align="middle"
                className={style['sum-card']}
                style={{ borderLeft: '6px solid #48A1FF' }}
                onClick={() => setPlanDialogVisiable(true)}
              >
                <Col span={20}>
                  <div className={style['sum-number']}>{sumData?.totalPlanNum}</div>
                  <div className={style['sum-describe']}>计划总数</div>
                </Col>
                <Col span={4} className={style['img-col']}>
                  {/* <Image src={sumImg} preview={false} /> */}
                  <PlanListDialog
                    closeModal={() => setPlanDialogVisiable(false)}
                    searchParams={searchParams}
                    visible={planDialogVisiable}
                  />
                </Col>
              </Row>
            </Col>
            <Col span={3}>
              <Row
                align="middle"
                className={style['sum-card']}
                style={{ borderLeft: '6px solid  #FBD44B' }}
              >
                <Col span={20}>
                  <div className={style['sum-number']}>{sumData?.totalTaskNum}</div>
                  <div className={style['sum-describe']}>任务总数</div>
                </Col>
                <Col span={4} className={style['img-col']}>
                  {/* <Image src={carImg} preview={false} /> */}
                </Col>
              </Row>
            </Col>
            <Col span={3}>
              <Row
                align="middle"
                className={style['sum-card']}
                style={{ borderLeft: '6px solid #5ACC75' }}
              >
                <Col span={20}>
                  <div className={style['sum-number']}>{sumData?.finishedTaskNum}</div>
                  <div className={style['sum-describe']}>已完成任务数</div>
                </Col>
                <Col span={4} className={style['img-col']}>
                  {/* <Image src={recordImg} preview={false} /> */}
                </Col>
              </Row>
            </Col>
            <Col span={3}>
              <Row
                align="middle"
                className={style['sum-card']}
                style={{ borderLeft: '6px solid #F04864' }}
              >
                <Col span={20}>
                  <div className={style['sum-number']}>{sumData?.notFinishedTaskNum}</div>
                  <div className={style['sum-describe']}>未完成任务数</div>
                </Col>
                <Col span={4} className={style['img-col']}>
                  {/* <Image src={warnImg} preview={false} /> */}
                </Col>
              </Row>
            </Col>
            <Col span={3}>
              <Row
                align="middle"
                className={style['sum-card']}
                style={{ borderLeft: '6px solid rgba(83, 215, 255, 1)' }}
              >
                <Col span={20}>
                  <div className={style['sum-number']}>{sumData?.totalExceptionNum}</div>
                  <div className={style['sum-describe']}>异常总数</div>
                </Col>
                <Col span={4} className={style['img-col']}>
                  {/* <Image src={warnImg} preview={false} /> */}
                </Col>
              </Row>
            </Col>
            <Col span={3}>
              <Row
                align="middle"
                className={style['sum-card']}
                style={{ borderLeft: '6px solid #e26cffff' }}
              >
                <Col span={20}>
                  <div className={style['sum-number']}>{sumData?.notHandleExceptionNum}</div>
                  <div className={style['sum-describe']}>未处置异常总数</div>
                </Col>
                <Col span={4} className={style['img-col']}>
                  {/* <Image src={warnImg} preview={false} /> */}
                </Col>
              </Row>
            </Col>
            <Col span={3}>
              <Row
                align="middle"
                className={style['sum-card']}
                style={{ borderLeft: '6px solid #48f0adff' }}
              >
                <Col span={20}>
                  <div className={style['sum-number']}>{sumData?.totalAlarmNum}</div>
                  <div className={style['sum-describe']}>报警总数</div>
                </Col>
                <Col span={4} className={style['img-col']}>
                  {/* <Image src={warnImg} preview={false} /> */}
                </Col>
              </Row>
            </Col>
            <Col span={3}>
              <Row
                align="middle"
                className={[style['sum-card'], style.last].join(' ')}
                style={{ borderLeft: '6px solid rgba(240, 223, 72, 1)' }}
              >
                <Col span={20}>
                  <div className={style['sum-number']}>{sumData?.notHandleAlarmNum}</div>
                  <div className={style['sum-describe']}>未处置报警总数</div>
                </Col>
                <Col span={4} className={style['img-col']}>
                  {/* <Image src={warnImg} preview={false} /> */}
                </Col>
              </Row>
            </Col>
          </Row>
        </div>

        <div className={style['pie-card-box']}>
          <Row align="middle" gutter={10}>
            <Col span={17}>
              <div className={style['trend-card-box']}>
                <Card title="巡检任务执行趋势" style={{ width: '100%' }}>
                  <div
                    className="trend-chart"
                    id="trend-chart"
                    style={{
                      width: '100%',
                      height: '350px',
                    }}
                  />
                </Card>
              </div>
            </Col>
            <Col span={7}>
              <div className={style['trend-card-box']}>
                <Card title="告警来源占比" style={{ width: '100%' }}>
                  <div
                    id="pie-chart-1"
                    className="trend-chart"
                    style={{
                      width: '100%',
                      height: '350px',
                    }}
                  />
                </Card>
              </div>
            </Col>
          </Row>
        </div>
      </Spin>
    </div>
  );
};

export default YTHLocalization.withLocal(
  ElecWaybillList,
  locales['zh-CN'],
  YTHLocalization.getLanguage(),
);
