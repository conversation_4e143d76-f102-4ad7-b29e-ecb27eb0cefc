import { QueryParams } from '@/types/statistics';
import { <PERSON><PERSON>, Button } from 'antd';
import React, { useEffect, useRef, useMemo, useCallback } from 'react';
import YTHList, { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { PlanPageResponse, PlanQueryParam, PlanQueryParamFilter } from '@/types/inspection/plan';
import PlanApi from '@/service/inspection/planApi';
import style from '../statistics.module.less';

/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗传入的数据 */
  searchParams: QueryParams;
  /** 弹窗是否可见 */
  visible: boolean;
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
};

/**
 * @description 查看 或新增 modal
 * @param PropsTypes PropsTypes
 */
const PlanListDialog: React.FC<PropsTypes> = ({ searchParams, visible, closeModal = () => {} }) => {
  // 使用 useMemo 确保 listAction 只创建一次
  const listAction: ActionType = useMemo(() => YTHList.createAction(), []);
  const listActionRef: React.MutableRefObject<ActionType | undefined> =
    useRef<ActionType>(listAction);

  // 表格列配置
  const columns: IYTHColumnProps[] = useMemo(
    () => [{ dataIndex: 'serialNo', title: '序号', width: 80, display: false }],
    [],
  );

  // 处理弹窗关闭的回调函数
  const handleCloseModal: () => void = useCallback(() => {
    closeModal();
  }, [closeModal]);

  // 加载数据
  useEffect(() => {
    if (!visible) {
      return;
    }
    // 当弹窗显示时，可以在这里处理搜索参数
    // console.log('searchParams', searchParams);
  }, [searchParams, visible]);

  /** 处理查询参数：从选择器数组中提取第一个选项的 code 值 */
  const handleFilter: (filter: PlanQueryParamFilter) => PlanQueryParam = (
    filter: PlanQueryParamFilter,
  ): PlanQueryParam => {
    return {
      // Input 组件直接取值
      planCode: filter.planCode,
      planName: filter.planName,
      directorUserName: filter.directorUserName,
      // Selector 组件从选中数组的第一个对象中提取 code
      inspectionMethod: filter.inspectionMethod?.[0]?.code,
      planType: filter.planType?.[0]?.code,
      executionFrequency: filter.executionFrequency?.[0]?.code,
      isUsed: filter.isUsed?.[0]?.code,
    };
  };
  return (
    <Modal
      width="80%"
      title="计划列表"
      style={{ top: 30 }}
      visible={visible}
      destroyOnClose
      onCancel={handleCloseModal}
      maskClosable={false}
      footer={[
        <Button key="cancel" onClick={handleCloseModal} className={style['reset-btn']}>
          取消
        </Button>,
      ]}
    >
      <YTHList
        defaultQuery={{}}
        code="InspectionPlanList"
        action={listAction}
        actionRef={listActionRef}
        showRowSelection={false}
        extraOperation={[]}
        operation={[]}
        listKey="id"
        request={async (filter, pagination) => {
          try {
            const resData: PlanPageResponse = await PlanApi.queryByPage({
              aescs: [],
              descs: [],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            });
            if (resData.code && resData.code === 200) {
              resData.data.forEach((_, index) => {
                resData.data[index].serialNo =
                  (pagination.current - 1) * pagination.pageSize + index + 1;
              });
              return {
                data: resData.data,
                total: resData.total,
                success: true,
              };
            }
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch {
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        columns={columns}
      />
    </Modal>
  );
};

export default PlanListDialog;
